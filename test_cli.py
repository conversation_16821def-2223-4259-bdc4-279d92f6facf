#!/usr/bin/env python3
"""
Test script for CLI functionality.
This script tests the CLI commands without requiring actual API calls.
"""

import sys
import tempfile
from pathlib import Path
from typer.testing import Cli<PERSON><PERSON>ner

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from translator.cli.main import app
from translator.utils import setup_logger


def test_version_command():
    """Test version command."""
    print("📋 Testing Version Command")
    print("=" * 50)
    
    runner = CliRunner()
    result = runner.invoke(app, ["version"])
    
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")
    
    assert result.exit_code == 0
    assert "Translator version" in result.stdout
    print("✅ Version command test passed")


def test_info_command():
    """Test info command."""
    print("\n📊 Testing Info Command")
    print("=" * 50)
    
    runner = CliRunner()
    result = runner.invoke(app, ["info"])
    
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")
    
    assert result.exit_code == 0
    assert "Translator Information" in result.stdout
    print("✅ Info command test passed")


def test_config_commands():
    """Test configuration commands."""
    print("\n🔧 Testing Config Commands")
    print("=" * 50)
    
    runner = CliRunner()
    
    # Test config show without file (should fail)
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        config_file = temp_path / "test_config.yaml"
        
        print("Testing config show (no file)...")
        result = runner.invoke(app, ["config", "--show", "--file", str(config_file)])
        print(f"Exit code: {result.exit_code}")
        assert result.exit_code == 1  # Should fail
        print("✅ Config show (no file) test passed")
        
        # Test config validation without file (should fail)
        print("Testing config validate (no file)...")
        result = runner.invoke(app, ["config", "--validate", "--file", str(config_file)])
        print(f"Exit code: {result.exit_code}")
        assert result.exit_code == 1  # Should fail
        print("✅ Config validate (no file) test passed")


def test_help_commands():
    """Test help commands."""
    print("\n❓ Testing Help Commands")
    print("=" * 50)
    
    runner = CliRunner()
    
    # Test main help
    print("Testing main help...")
    result = runner.invoke(app, ["--help"])
    print(f"Exit code: {result.exit_code}")
    assert result.exit_code == 0
    assert "AI-powered file translator" in result.stdout
    print("✅ Main help test passed")
    
    # Test translate help
    print("Testing translate help...")
    result = runner.invoke(app, ["translate", "--help"])
    print(f"Exit code: {result.exit_code}")
    assert result.exit_code == 0
    assert "Translate a document file" in result.stdout
    print("✅ Translate help test passed")
    
    # Test config help
    print("Testing config help...")
    result = runner.invoke(app, ["config", "--help"])
    print(f"Exit code: {result.exit_code}")
    assert result.exit_code == 0
    assert "Manage configuration" in result.stdout
    print("✅ Config help test passed")


def test_translate_command_validation():
    """Test translate command input validation."""
    print("\n🌍 Testing Translate Command Validation")
    print("=" * 50)
    
    runner = CliRunner()
    
    # Test with non-existent file
    print("Testing translate with non-existent file...")
    result = runner.invoke(app, ["translate", "nonexistent.docx"])
    print(f"Exit code: {result.exit_code}")
    assert result.exit_code == 2  # Typer validation error
    print("✅ Non-existent file validation test passed")


def test_batch_command_validation():
    """Test batch command input validation."""
    print("\n📁 Testing Batch Command Validation")
    print("=" * 50)
    
    runner = CliRunner()
    
    # Test with non-existent directory
    print("Testing batch with non-existent directory...")
    result = runner.invoke(app, ["batch", "nonexistent_dir"])
    print(f"Exit code: {result.exit_code}")
    assert result.exit_code == 2  # Typer validation error
    print("✅ Non-existent directory validation test passed")


def test_cli_structure():
    """Test CLI command structure."""
    print("\n🏗️ Testing CLI Structure")
    print("=" * 50)
    
    runner = CliRunner()
    
    # Get list of available commands
    result = runner.invoke(app, ["--help"])
    output = result.stdout
    
    # Check for expected commands
    expected_commands = ["translate", "config", "batch", "info", "version"]
    
    for command in expected_commands:
        assert command in output, f"Command '{command}' not found in help output"
        print(f"✅ Command '{command}' found")
    
    print("✅ CLI structure test passed")


def test_rich_formatting():
    """Test Rich formatting in CLI output."""
    print("\n🎨 Testing Rich Formatting")
    print("=" * 50)
    
    runner = CliRunner()
    
    # Test info command for rich formatting
    result = runner.invoke(app, ["info"])
    
    # Check for Rich markup (this is basic - Rich formatting might not show in test output)
    assert result.exit_code == 0
    assert len(result.stdout) > 0
    print("✅ Rich formatting test passed")


def main():
    """Run all CLI tests."""
    
    # Setup logging
    setup_logger(level="INFO")
    
    print("🧪 CLI Test Suite")
    print("=" * 60)
    
    try:
        # Run tests
        test_version_command()
        test_info_command()
        test_config_commands()
        test_help_commands()
        test_translate_command_validation()
        test_batch_command_validation()
        test_cli_structure()
        test_rich_formatting()
        
        print("\n🎉 All CLI tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
