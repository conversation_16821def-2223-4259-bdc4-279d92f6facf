#!/usr/bin/env python3
"""
Test script to verify the fixes for DOCX table translation and PDF saving issues.
"""

import sys
import tempfile
from pathlib import Path
from docx import Document
from reportlab.platypus import SimpleDocTemplate, Paragraph
from reportlab.lib.styles import getSampleStyleSheet

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from translator.file_processor import FileProcessorFactory
from translator.utils import setup_logger


def create_test_docx_with_table(file_path: Path) -> None:
    """Create a test DOCX file with tables."""
    doc = Document()
    
    # Add title
    doc.add_heading('Test Document with Tables', 0)
    
    # Add paragraph
    doc.add_paragraph('This document contains both paragraphs and tables.')
    
    # Add a table
    table = doc.add_table(rows=3, cols=3)
    table.style = 'Table Grid'
    
    # Fill table with test data
    cells_data = [
        ['Header 1', 'Header 2', 'Header 3'],
        ['Row 1 Cell 1', 'Row 1 Cell 2', 'Row 1 Cell 3'],
        ['Row 2 Cell 1', 'Row 2 Cell 2', 'Row 2 Cell 3']
    ]
    
    for row_idx, row_data in enumerate(cells_data):
        for col_idx, cell_data in enumerate(row_data):
            table.cell(row_idx, col_idx).text = cell_data
    
    # Add another paragraph
    doc.add_paragraph('This is a paragraph after the table.')
    
    # Add another table
    table2 = doc.add_table(rows=2, cols=2)
    table2.style = 'Table Grid'
    table2.cell(0, 0).text = 'Name'
    table2.cell(0, 1).text = 'Value'
    table2.cell(1, 0).text = 'Test Item'
    table2.cell(1, 1).text = 'Test Value'
    
    # Add final paragraph
    doc.add_paragraph('This is the final paragraph.')
    
    doc.save(file_path)
    print(f"✅ Created test DOCX with tables: {file_path}")


def create_test_pdf_with_config(file_path: Path) -> None:
    """Create a test PDF file."""
    doc = SimpleDocTemplate(str(file_path))
    styles = getSampleStyleSheet()
    
    story = []
    story.append(Paragraph("Test PDF Document", styles['Title']))
    story.append(Paragraph("This is a test paragraph for PDF translation.", styles['Normal']))
    story.append(Paragraph("Another paragraph with some content.", styles['Normal']))
    
    doc.build(story)
    print(f"✅ Created test PDF: {file_path}")


def test_docx_table_processing():
    """Test DOCX table processing functionality."""
    print("📄 Testing DOCX Table Processing")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create test DOCX with tables
        docx_file = temp_path / "test_with_tables.docx"
        create_test_docx_with_table(docx_file)
        
        # Test DOCX processing
        print("Testing DOCX text extraction...")
        config = {"preserve_tables": True, "preserve_styles": True}
        docx_processor = FileProcessorFactory.create_processor(docx_file, config)
        
        # Extract text
        extracted_text = docx_processor.extract_text(docx_file)
        print(f"Extracted text length: {len(extracted_text)} characters")
        print("Extracted text preview:")
        print("-" * 30)
        print(extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text)
        print("-" * 30)
        
        # Check if table content is included
        assert "Header 1" in extracted_text, "Table headers not found in extracted text"
        assert "Row 1 Cell 1" in extracted_text, "Table cell content not found"
        assert "|" in extracted_text, "Table structure markers not found"
        
        # Extract structure
        doc_structure = docx_processor.extract_structure(docx_file)
        print(f"Document segments: {len(doc_structure.segments)}")
        
        for i, segment in enumerate(doc_structure.segments):
            print(f"Segment {i}: [{segment.segment_type}] {segment.text[:50]}...")
        
        # Test saving with mock translated text
        print("\nTesting DOCX saving with translated content...")
        output_file = temp_path / "translated_test.docx"
        
        # Create mock translated text that includes table structure
        mock_translated = """Translated Title Document with Tables

This document contains both translated paragraphs and tables.

Translated Header 1 | Translated Header 2 | Translated Header 3
Translated Row 1 Cell 1 | Translated Row 1 Cell 2 | Translated Row 1 Cell 3
Translated Row 2 Cell 1 | Translated Row 2 Cell 2 | Translated Row 2 Cell 3

This is a translated paragraph after the table.

Translated Name | Translated Value
Translated Test Item | Translated Test Value

This is the translated final paragraph."""
        
        try:
            docx_processor.save_translated_file(
                docx_file, mock_translated, output_file, preserve_formatting=True
            )
            print(f"✅ DOCX saved successfully: {output_file}")
            
            # Verify the saved file
            if output_file.exists():
                print(f"✅ Output file exists and is {output_file.stat().st_size} bytes")
            else:
                print("❌ Output file was not created")
                
        except Exception as e:
            print(f"❌ DOCX saving failed: {e}")
            raise
        
        print("✅ DOCX table processing tests passed")


def test_pdf_saving_with_various_configs():
    """Test PDF saving with various configuration types."""
    print("\n📄 Testing PDF Saving with Various Configs")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Test different configuration scenarios
        test_configs = [
            {"font_size": 12, "margin": 72},  # Normal numeric values
            {"font_size": "12", "margin": "72"},  # String values (should be converted)
            {"font_size": "invalid", "margin": "invalid"},  # Invalid values (should use defaults)
            {},  # Empty config (should use defaults)
        ]
        
        for i, config in enumerate(test_configs):
            print(f"\nTesting config {i+1}: {config}")
            
            try:
                # Create PDF processor
                pdf_file = temp_path / f"test_{i}.pdf"
                create_test_pdf_with_config(pdf_file)
                
                pdf_processor = FileProcessorFactory.create_processor(pdf_file, config)
                
                # Extract text
                extracted_text = pdf_processor.extract_text(pdf_file)
                print(f"Extracted text length: {len(extracted_text)} characters")
                
                # Test saving
                output_file = temp_path / f"translated_test_{i}.pdf"
                mock_translated = "Translated PDF Document\n\nThis is a translated test paragraph for PDF translation.\n\nAnother translated paragraph with some content."
                
                pdf_processor.save_translated_file(
                    pdf_file, mock_translated, output_file, preserve_formatting=True
                )
                
                print(f"✅ PDF config {i+1} saved successfully: {output_file}")
                
                if output_file.exists():
                    print(f"✅ Output file exists and is {output_file.stat().st_size} bytes")
                else:
                    print("❌ Output file was not created")
                    
            except Exception as e:
                print(f"❌ PDF config {i+1} failed: {e}")
                raise
        
        print("✅ PDF saving tests passed")


def test_error_handling():
    """Test error handling for edge cases."""
    print("\n🚨 Testing Error Handling")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Test with malformed table text
        docx_file = temp_path / "test.docx"
        create_test_docx_with_table(docx_file)
        
        config = {"preserve_tables": True}
        docx_processor = FileProcessorFactory.create_processor(docx_file, config)
        
        # Test with malformed translated text
        output_file = temp_path / "error_test.docx"
        malformed_text = "Some text\n\nMalformed | table | with | too | many | columns\n\nMore text"
        
        try:
            docx_processor.save_translated_file(
                docx_file, malformed_text, output_file, preserve_formatting=True
            )
            print("✅ Handled malformed table text gracefully")
        except Exception as e:
            print(f"⚠️ Error handling test: {e}")
        
        print("✅ Error handling tests completed")


def main():
    """Run all fix verification tests."""
    
    # Setup logging
    setup_logger(level="INFO")
    
    print("🧪 Fix Verification Test Suite")
    print("=" * 60)
    
    try:
        # Run tests
        test_docx_table_processing()
        test_pdf_saving_with_various_configs()
        test_error_handling()
        
        print("\n🎉 All fix verification tests completed successfully!")
        print("\n📋 Fixes Verified:")
        print("  ✅ DOCX table translation functionality")
        print("  ✅ PDF saving with numeric type handling")
        print("  ✅ Error handling for edge cases")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
