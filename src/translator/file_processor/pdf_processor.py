"""PDF file processor using pdfplumber and reportlab."""

import pdfplumber
from pathlib import Path
from typing import List, Dict, Any
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

from .base import BaseFileProcessor, TextSegment, DocumentStructure
from ..utils.exceptions import FileProcessingError


class PdfProcessor(BaseFileProcessor):
    """PDF file processor using pdfplumber for extraction and reportlab for generation."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize PDF processor."""
        super().__init__(config)
        self.page_size = config.get("page_size", A4)
        self.font_name = config.get("font_name", "Helvetica")
        self.font_size = config.get("font_size", 12)
        self.margin = config.get("margin", 1 * inch)

        # Try to register Chinese font for better support
        self._register_fonts()

    def _register_fonts(self):
        """Register fonts for better Unicode support."""
        try:
            # Try to register a Unicode-capable font
            # This is optional and will fall back to default fonts if not available
            pass
        except Exception:
            # Use default fonts if custom fonts are not available
            pass

    def extract_text(self, file_path: Path) -> str:
        """
        Extract plain text from PDF file.

        Args:
            file_path: Path to PDF file

        Returns:
            Extracted text content

        Raises:
            FileProcessingError: If extraction fails
        """
        try:
            text_parts = []

            with pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    page_text = page.extract_text()
                    if page_text:
                        text_parts.append(page_text)
                        self.logger.debug(f"Extracted text from page {page_num + 1}")

            if not text_parts:
                raise FileProcessingError("No text content found in PDF")

            return "\n\n".join(text_parts)

        except Exception as e:
            raise FileProcessingError(f"Failed to extract text from PDF: {str(e)}")

    def extract_structure(self, file_path: Path) -> DocumentStructure:
        """
        Extract structured content from PDF file.

        Args:
            file_path: Path to PDF file

        Returns:
            Document structure with basic formatting information

        Raises:
            FileProcessingError: If extraction fails
        """
        try:
            segments = []
            position = 0

            with pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    page_text = page.extract_text()
                    if page_text:
                        # Split page text into paragraphs
                        paragraphs = [p.strip() for p in page_text.split('\n\n') if p.strip()]

                        for para in paragraphs:
                            formatting = {
                                "page_number": page_num + 1,
                                "font_info": self._extract_font_info(page, para),
                                "position_info": self._extract_position_info(page, para)
                            }

                            segment = TextSegment(
                                text=para,
                                formatting=formatting,
                                position=position,
                                segment_type="paragraph"
                            )
                            segments.append(segment)
                            position += 1

            metadata = {
                "total_pages": len(pdf.pages) if 'pdf' in locals() else 0,
                "pdf_info": self._extract_pdf_metadata(file_path)
            }

            return DocumentStructure(
                segments=segments,
                metadata=metadata,
                original_format="pdf"
            )

        except Exception as e:
            raise FileProcessingError(f"Failed to extract structure from PDF: {str(e)}")

    def _extract_font_info(self, page, text: str) -> Dict[str, Any]:
        """Extract font information from page (simplified)."""
        # This is a simplified implementation
        # In a more advanced version, you could analyze the actual font data
        # Parameters are kept for future enhancement
        _ = page, text  # Suppress unused parameter warnings
        return {
            "estimated_font_size": 12,
            "estimated_font_family": "default"
        }

    def _extract_position_info(self, page, text: str) -> Dict[str, Any]:
        """Extract position information (simplified)."""
        _ = text  # Suppress unused parameter warning
        return {
            "page_width": page.width,
            "page_height": page.height,
            "estimated_position": "body"
        }

    def _extract_pdf_metadata(self, file_path: Path) -> Dict[str, Any]:
        """Extract PDF metadata."""
        try:
            with pdfplumber.open(file_path) as pdf:
                metadata = pdf.metadata or {}
                return {
                    "title": metadata.get("Title", ""),
                    "author": metadata.get("Author", ""),
                    "subject": metadata.get("Subject", ""),
                    "creator": metadata.get("Creator", ""),
                    "producer": metadata.get("Producer", ""),
                    "creation_date": str(metadata.get("CreationDate", "")),
                    "modification_date": str(metadata.get("ModDate", ""))
                }
        except Exception:
            return {}

    def save_translated_file(
        self,
        original_path: Path,
        translated_text: str,
        output_path: Path,
        preserve_formatting: bool = True
    ) -> None:
        """
        Save translated text to PDF file.

        Args:
            original_path: Path to original file (unused in simple PDF generation)
            translated_text: Translated text content
            output_path: Path to output file
            preserve_formatting: Whether to preserve basic formatting

        Raises:
            FileProcessingError: If saving fails
        """
        try:
            _ = original_path  # Suppress unused parameter warning
            self._create_pdf_document(translated_text, output_path, preserve_formatting)
            self.logger.info(f"PDF saved: {output_path}")

        except Exception as e:
            raise FileProcessingError(f"Failed to save translated PDF: {str(e)}")

    def _create_pdf_document(self, text: str, output_path: Path, preserve_formatting: bool) -> None:
        """Create PDF document with translated text."""
        _ = preserve_formatting  # Suppress unused parameter warning (for future use)
        # Create document
        doc = SimpleDocTemplate(
            str(output_path),
            pagesize=self.page_size,
            rightMargin=self.margin,
            leftMargin=self.margin,
            topMargin=self.margin,
            bottomMargin=self.margin
        )

        # Get styles
        styles = getSampleStyleSheet()
        normal_style = styles['Normal']

        # Create custom style for better Unicode support
        custom_style = ParagraphStyle(
            'CustomNormal',
            parent=normal_style,
            fontSize=self.font_size,
            fontName=self.font_name,
            leading=self.font_size * 1.2,
            spaceAfter=12
        )

        # Build content
        story = []

        # Split text into paragraphs
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]

        for para_text in paragraphs:
            if para_text:
                # Create paragraph with proper encoding
                try:
                    para = Paragraph(para_text, custom_style)
                    story.append(para)
                    story.append(Spacer(1, 6))
                except Exception as e:
                    # Fallback for problematic text
                    self.logger.warning(f"Failed to create paragraph, using fallback: {e}")
                    safe_text = para_text.encode('ascii', 'ignore').decode('ascii')
                    para = Paragraph(safe_text, custom_style)
                    story.append(para)
                    story.append(Spacer(1, 6))

        # Build PDF
        doc.build(story)

    def save_translated_structure(
        self,
        original_structure: DocumentStructure,
        translated_segments: List[str],
        output_path: Path
    ) -> None:
        """
        Save translated content with preserved structure.

        Args:
            original_structure: Original document structure
            translated_segments: List of translated text segments
            output_path: Path to output file

        Raises:
            FileProcessingError: If saving fails
        """
        try:
            if len(translated_segments) != len(original_structure.segments):
                raise FileProcessingError("Mismatch between segments and translations")

            # Create document
            doc = SimpleDocTemplate(
                str(output_path),
                pagesize=self.page_size,
                rightMargin=self.margin,
                leftMargin=self.margin,
                topMargin=self.margin,
                bottomMargin=self.margin
            )

            styles = getSampleStyleSheet()
            story = []

            # Process each segment with its formatting
            for segment, translation in zip(original_structure.segments, translated_segments):
                style = self._create_style_from_formatting(styles, segment.formatting)

                try:
                    para = Paragraph(translation, style)
                    story.append(para)
                    story.append(Spacer(1, 6))
                except Exception as e:
                    self.logger.warning(f"Failed to create formatted paragraph: {e}")
                    # Fallback to normal style
                    para = Paragraph(translation, styles['Normal'])
                    story.append(para)
                    story.append(Spacer(1, 6))

            doc.build(story)
            self.logger.info(f"Structured PDF saved: {output_path}")

        except Exception as e:
            raise FileProcessingError(f"Failed to save structured PDF: {str(e)}")

    def _create_style_from_formatting(self, styles, formatting: Dict[str, Any]) -> ParagraphStyle:
        """Create paragraph style from formatting information."""
        base_style = styles['Normal']

        # Extract formatting information
        font_info = formatting.get("font_info", {})
        font_size = font_info.get("estimated_font_size", self.font_size)

        # Create custom style
        custom_style = ParagraphStyle(
            'Custom',
            parent=base_style,
            fontSize=font_size,
            fontName=self.font_name,
            leading=font_size * 1.2,
            spaceAfter=12
        )

        return custom_style

    def get_supported_extensions(self) -> List[str]:
        """Get supported file extensions."""
        return [".pdf"]
