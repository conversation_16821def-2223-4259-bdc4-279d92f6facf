"""DOCX file processor using python-docx."""

import re
from pathlib import Path
from typing import List, Dict, Any
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

from .base import BaseFileProcessor, TextSegment, DocumentStructure
from ..utils.exceptions import FileProcessingError


class DocxProcessor(BaseFileProcessor):
    """DOCX file processor using python-docx library."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize DOCX processor."""
        super().__init__(config)
        self.preserve_styles = config.get("preserve_styles", True)
        self.preserve_tables = config.get("preserve_tables", True)

    def extract_text(self, file_path: Path) -> str:
        """
        Extract plain text from DOCX file including tables.

        Args:
            file_path: Path to DOCX file

        Returns:
            Extracted text content

        Raises:
            FileProcessingError: If extraction fails
        """
        try:
            doc = Document(file_path)
            text_parts = []

            # Process document elements in order
            for element in doc.element.body:
                if element.tag.endswith('p'):  # Paragraph
                    # Find corresponding paragraph object
                    for para in doc.paragraphs:
                        if para._element == element and para.text.strip():
                            text_parts.append(para.text)
                            break
                elif element.tag.endswith('tbl'):  # Table
                    # Find corresponding table object
                    for table in doc.tables:
                        if table._element == element:
                            table_text = self._extract_table_text(table)
                            if table_text.strip():
                                text_parts.append(table_text)
                            break

            return "\n\n".join(text_parts)

        except Exception as e:
            raise FileProcessingError(f"Failed to extract text from DOCX: {str(e)}")

    def _extract_table_text(self, table) -> str:
        """Extract text from table maintaining structure."""
        table_rows = []
        for row in table.rows:
            row_cells = []
            for cell in row.cells:
                # Extract text from all paragraphs in cell
                cell_text = []
                for paragraph in cell.paragraphs:
                    if paragraph.text.strip():
                        cell_text.append(paragraph.text.strip())
                row_cells.append(" ".join(cell_text))
            if any(cell.strip() for cell in row_cells):  # Only add non-empty rows
                table_rows.append(" | ".join(row_cells))
        return "\n".join(table_rows)

    def extract_structure(self, file_path: Path) -> DocumentStructure:
        """
        Extract structured content from DOCX file.

        Args:
            file_path: Path to DOCX file

        Returns:
            Document structure with formatting information

        Raises:
            FileProcessingError: If extraction fails
        """
        try:
            doc = Document(file_path)
            segments = []
            position = 0

            # Extract paragraphs with formatting
            for para in doc.paragraphs:
                if para.text.strip():
                    formatting = self._extract_paragraph_formatting(para)
                    segment = TextSegment(
                        text=para.text,
                        formatting=formatting,
                        position=position,
                        segment_type="paragraph"
                    )
                    segments.append(segment)
                    position += 1

            # Extract tables
            for table in doc.tables:
                table_data = self._extract_table_data(table)
                if table_data:
                    segment = TextSegment(
                        text=table_data["text"],
                        formatting=table_data["formatting"],
                        position=position,
                        segment_type="table"
                    )
                    segments.append(segment)
                    position += 1

            metadata = {
                "total_paragraphs": len(doc.paragraphs),
                "total_tables": len(doc.tables),
                "core_properties": self._extract_core_properties(doc)
            }

            return DocumentStructure(
                segments=segments,
                metadata=metadata,
                original_format="docx"
            )

        except Exception as e:
            raise FileProcessingError(f"Failed to extract structure from DOCX: {str(e)}")

    def _extract_paragraph_formatting(self, paragraph) -> Dict[str, Any]:
        """Extract formatting information from paragraph."""
        formatting = {
            "style": paragraph.style.name if paragraph.style else "Normal",
            "alignment": paragraph.alignment,
            "runs": []
        }

        # Extract run-level formatting
        for run in paragraph.runs:
            run_formatting = {
                "text": run.text,
                "bold": run.bold,
                "italic": run.italic,
                "underline": run.underline,
                "font_name": run.font.name if run.font.name else None,
                "font_size": run.font.size.pt if run.font.size else None
            }
            formatting["runs"].append(run_formatting)

        return formatting

    def _extract_table_data(self, table) -> Dict[str, Any]:
        """Extract data and formatting from table."""
        table_text = []
        table_formatting = {
            "rows": len(table.rows),
            "columns": len(table.columns),
            "cells": []
        }

        for row in table.rows:
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip()
                row_text.append(cell_text)
                table_formatting["cells"].append({
                    "text": cell_text,
                    "paragraphs": len(cell.paragraphs)
                })
            table_text.append(" | ".join(row_text))

        return {
            "text": "\n".join(table_text),
            "formatting": table_formatting
        }

    def _extract_core_properties(self, doc) -> Dict[str, Any]:
        """Extract document core properties."""
        props = doc.core_properties
        return {
            "title": props.title,
            "author": props.author,
            "subject": props.subject,
            "created": props.created.isoformat() if props.created else None,
            "modified": props.modified.isoformat() if props.modified else None
        }

    def save_translated_file(
        self,
        original_path: Path,
        translated_text: str,
        output_path: Path,
        preserve_formatting: bool = True
    ) -> None:
        """
        Save translated text to DOCX file.

        Args:
            original_path: Path to original file
            translated_text: Translated text content
            output_path: Path to output file
            preserve_formatting: Whether to preserve original formatting

        Raises:
            FileProcessingError: If saving fails
        """
        try:
            if preserve_formatting:
                # Load original document and replace text while preserving structure
                self._save_with_formatting(original_path, translated_text, output_path)
            else:
                # Create new document with translated text
                self._save_simple(translated_text, output_path)

        except Exception as e:
            raise FileProcessingError(f"Failed to save translated DOCX: {str(e)}")

    def _save_simple(self, translated_text: str, output_path: Path) -> None:
        """Save translated text as simple DOCX document."""
        doc = Document()

        # Split text into paragraphs
        paragraphs = translated_text.split('\n\n')

        for para_text in paragraphs:
            if para_text.strip():
                doc.add_paragraph(para_text.strip())

        doc.save(output_path)
        self.logger.info(f"Simple DOCX saved: {output_path}")

    def _save_with_formatting(self, original_path: Path, translated_text: str, output_path: Path) -> None:
        """Save translated text while preserving original formatting including tables."""
        # Load original document
        doc = Document(original_path)

        # Split translated text into segments
        translated_segments = [p.strip() for p in translated_text.split('\n\n') if p.strip()]
        segment_index = 0

        # Process document elements in order
        for element in doc.element.body:
            if element.tag.endswith('p'):  # Paragraph
                # Find corresponding paragraph object
                for para in doc.paragraphs:
                    if para._element == element and para.text.strip():
                        if segment_index < len(translated_segments):
                            # Check if this is a table-like text (contains |)
                            if '|' not in translated_segments[segment_index]:
                                self._replace_paragraph_text(para, translated_segments[segment_index])
                                segment_index += 1
                        break
            elif element.tag.endswith('tbl'):  # Table
                # Find corresponding table object
                for table in doc.tables:
                    if table._element == element:
                        if segment_index < len(translated_segments):
                            # Check if this is table text (contains |)
                            if '|' in translated_segments[segment_index]:
                                self._replace_table_text(table, translated_segments[segment_index])
                                segment_index += 1
                        break

        doc.save(output_path)
        self.logger.info(f"Formatted DOCX saved: {output_path}")

    def _replace_table_text(self, table, translated_table_text: str) -> None:
        """Replace table text while preserving structure."""
        try:
            # Split translated table text into rows
            translated_rows = [row.strip() for row in translated_table_text.split('\n') if row.strip()]

            # Replace cell content
            for row_idx, table_row in enumerate(table.rows):
                if row_idx < len(translated_rows):
                    # Split row into cells
                    translated_cells = [cell.strip() for cell in translated_rows[row_idx].split('|')]

                    for cell_idx, table_cell in enumerate(table_row.cells):
                        if cell_idx < len(translated_cells):
                            # Clear existing paragraphs and add new text
                            for paragraph in table_cell.paragraphs:
                                paragraph.clear()

                            # Add translated text to first paragraph
                            if table_cell.paragraphs:
                                table_cell.paragraphs[0].add_run(translated_cells[cell_idx])
                            else:
                                table_cell.add_paragraph(translated_cells[cell_idx])

        except Exception as e:
            self.logger.warning(f"Failed to replace table text: {e}")
            # Fallback: leave table unchanged

    def _replace_paragraph_text(self, paragraph, new_text: str) -> None:
        """Replace paragraph text while preserving formatting."""
        # Clear existing runs
        for run in paragraph.runs:
            run.clear()

        # Add new text to first run or create new run
        if paragraph.runs:
            paragraph.runs[0].text = new_text
        else:
            paragraph.add_run(new_text)

    def save_translated_structure(
        self,
        original_structure: DocumentStructure,
        translated_segments: List[str],
        output_path: Path
    ) -> None:
        """
        Save translated content with preserved structure.

        Args:
            original_structure: Original document structure
            translated_segments: List of translated text segments
            output_path: Path to output file

        Raises:
            FileProcessingError: If saving fails
        """
        try:
            doc = Document()

            if len(translated_segments) != len(original_structure.segments):
                raise FileProcessingError("Mismatch between segments and translations")

            for segment, translation in zip(original_structure.segments, translated_segments):
                if segment.segment_type == "paragraph":
                    para = doc.add_paragraph(translation)
                    self._apply_paragraph_formatting(para, segment.formatting)
                elif segment.segment_type == "table":
                    self._add_translated_table(doc, translation, segment.formatting)

            doc.save(output_path)
            self.logger.info(f"Structured DOCX saved: {output_path}")

        except Exception as e:
            raise FileProcessingError(f"Failed to save structured DOCX: {str(e)}")

    def _apply_paragraph_formatting(self, paragraph, formatting: Dict[str, Any]) -> None:
        """Apply formatting to paragraph."""
        try:
            # Apply style if available
            if "style" in formatting and formatting["style"]:
                paragraph.style = formatting["style"]

            # Apply alignment
            if "alignment" in formatting and formatting["alignment"]:
                paragraph.alignment = formatting["alignment"]

        except Exception as e:
            self.logger.warning(f"Failed to apply paragraph formatting: {e}")

    def _add_translated_table(self, doc, translated_text: str, formatting: Dict[str, Any]) -> None:
        """Add translated table to document."""
        try:
            rows = translated_text.split('\n')
            if not rows:
                return

            # Parse table dimensions
            table_rows = formatting.get("rows", len(rows))
            table_cols = formatting.get("columns", len(rows[0].split('|')) if rows else 1)

            table = doc.add_table(rows=table_rows, cols=table_cols)

            for row_idx, row_text in enumerate(rows[:table_rows]):
                cells = row_text.split('|')
                for col_idx, cell_text in enumerate(cells[:table_cols]):
                    if row_idx < len(table.rows) and col_idx < len(table.rows[row_idx].cells):
                        table.rows[row_idx].cells[col_idx].text = cell_text.strip()

        except Exception as e:
            self.logger.warning(f"Failed to add translated table: {e}")
            # Fallback: add as paragraph
            doc.add_paragraph(translated_text)

    def get_supported_extensions(self) -> List[str]:
        """Get supported file extensions."""
        return [".docx"]
