"""DOCX file processor using python-docx."""

import re
from pathlib import Path
from typing import List, Dict, Any
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

from .base import BaseFileProcessor, TextSegment, DocumentStructure
from ..utils.exceptions import FileProcessingError


class DocxProcessor(BaseFileProcessor):
    """DOCX file processor using python-docx library."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize DOCX processor."""
        super().__init__(config)
        self.preserve_styles = config.get("preserve_styles", True)
        self.preserve_tables = config.get("preserve_tables", True)

    def extract_text(self, file_path: Path) -> str:
        """
        Extract plain text from DOCX file.

        Args:
            file_path: Path to DOCX file

        Returns:
            Extracted text content

        Raises:
            FileProcessingError: If extraction fails
        """
        try:
            doc = Document(file_path)
            text_parts = []

            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text)

            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text_parts.append(cell.text)

            return "\n\n".join(text_parts)

        except Exception as e:
            raise FileProcessingError(f"Failed to extract text from DOCX: {str(e)}")

    def extract_structure(self, file_path: Path) -> DocumentStructure:
        """
        Extract structured content from DOCX file.

        Args:
            file_path: Path to DOCX file

        Returns:
            Document structure with formatting information

        Raises:
            FileProcessingError: If extraction fails
        """
        try:
            doc = Document(file_path)
            segments = []
            position = 0

            # Extract paragraphs with formatting
            for para in doc.paragraphs:
                if para.text.strip():
                    formatting = self._extract_paragraph_formatting(para)
                    segment = TextSegment(
                        text=para.text,
                        formatting=formatting,
                        position=position,
                        segment_type="paragraph"
                    )
                    segments.append(segment)
                    position += 1

            # Extract tables
            for table in doc.tables:
                table_data = self._extract_table_data(table)
                if table_data:
                    segment = TextSegment(
                        text=table_data["text"],
                        formatting=table_data["formatting"],
                        position=position,
                        segment_type="table"
                    )
                    segments.append(segment)
                    position += 1

            metadata = {
                "total_paragraphs": len(doc.paragraphs),
                "total_tables": len(doc.tables),
                "core_properties": self._extract_core_properties(doc)
            }

            return DocumentStructure(
                segments=segments,
                metadata=metadata,
                original_format="docx"
            )

        except Exception as e:
            raise FileProcessingError(f"Failed to extract structure from DOCX: {str(e)}")

    def _extract_paragraph_formatting(self, paragraph) -> Dict[str, Any]:
        """Extract formatting information from paragraph."""
        formatting = {
            "style": paragraph.style.name if paragraph.style else "Normal",
            "alignment": paragraph.alignment,
            "runs": []
        }

        # Extract run-level formatting
        for run in paragraph.runs:
            run_formatting = {
                "text": run.text,
                "bold": run.bold,
                "italic": run.italic,
                "underline": run.underline,
                "font_name": run.font.name if run.font.name else None,
                "font_size": run.font.size.pt if run.font.size else None
            }
            formatting["runs"].append(run_formatting)

        return formatting

    def _extract_table_data(self, table) -> Dict[str, Any]:
        """Extract data and formatting from table."""
        table_text = []
        table_formatting = {
            "rows": len(table.rows),
            "columns": len(table.columns),
            "cells": []
        }

        for row in table.rows:
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip()
                row_text.append(cell_text)
                table_formatting["cells"].append({
                    "text": cell_text,
                    "paragraphs": len(cell.paragraphs)
                })
            table_text.append(" | ".join(row_text))

        return {
            "text": "\n".join(table_text),
            "formatting": table_formatting
        }

    def _extract_core_properties(self, doc) -> Dict[str, Any]:
        """Extract document core properties."""
        props = doc.core_properties
        return {
            "title": props.title,
            "author": props.author,
            "subject": props.subject,
            "created": props.created.isoformat() if props.created else None,
            "modified": props.modified.isoformat() if props.modified else None
        }

    def save_translated_file(
        self,
        original_path: Path,
        translated_text: str,
        output_path: Path,
        preserve_formatting: bool = True
    ) -> None:
        """
        Save translated text to DOCX file.

        Args:
            original_path: Path to original file
            translated_text: Translated text content
            output_path: Path to output file
            preserve_formatting: Whether to preserve original formatting

        Raises:
            FileProcessingError: If saving fails
        """
        try:
            if preserve_formatting:
                # Load original document and replace text while preserving structure
                self._save_with_formatting(original_path, translated_text, output_path)
            else:
                # Create new document with translated text
                self._save_simple(translated_text, output_path)

        except Exception as e:
            raise FileProcessingError(f"Failed to save translated DOCX: {str(e)}")

    def _save_simple(self, translated_text: str, output_path: Path) -> None:
        """Save translated text as simple DOCX document."""
        doc = Document()

        # Split text into paragraphs
        paragraphs = translated_text.split('\n\n')

        for para_text in paragraphs:
            if para_text.strip():
                doc.add_paragraph(para_text.strip())

        doc.save(output_path)
        self.logger.info(f"Simple DOCX saved: {output_path}")

    def _save_with_formatting(self, original_path: Path, translated_text: str, output_path: Path) -> None:
        """Save translated text while preserving original formatting."""
        # Load original document
        doc = Document(original_path)

        # Split translated text into paragraphs
        translated_paragraphs = [p.strip() for p in translated_text.split('\n\n') if p.strip()]

        # Replace paragraph text while preserving formatting
        para_index = 0
        for paragraph in doc.paragraphs:
            if paragraph.text.strip() and para_index < len(translated_paragraphs):
                self._replace_paragraph_text(paragraph, translated_paragraphs[para_index])
                para_index += 1

        doc.save(output_path)
        self.logger.info(f"Formatted DOCX saved: {output_path}")

    def _replace_paragraph_text(self, paragraph, new_text: str) -> None:
        """Replace paragraph text while preserving formatting."""
        # Clear existing runs
        for run in paragraph.runs:
            run.clear()

        # Add new text to first run or create new run
        if paragraph.runs:
            paragraph.runs[0].text = new_text
        else:
            paragraph.add_run(new_text)

    def save_translated_structure(
        self,
        original_structure: DocumentStructure,
        translated_segments: List[str],
        output_path: Path
    ) -> None:
        """
        Save translated content with preserved structure.

        Args:
            original_structure: Original document structure
            translated_segments: List of translated text segments
            output_path: Path to output file

        Raises:
            FileProcessingError: If saving fails
        """
        try:
            doc = Document()

            if len(translated_segments) != len(original_structure.segments):
                raise FileProcessingError("Mismatch between segments and translations")

            for segment, translation in zip(original_structure.segments, translated_segments):
                if segment.segment_type == "paragraph":
                    para = doc.add_paragraph(translation)
                    self._apply_paragraph_formatting(para, segment.formatting)
                elif segment.segment_type == "table":
                    self._add_translated_table(doc, translation, segment.formatting)

            doc.save(output_path)
            self.logger.info(f"Structured DOCX saved: {output_path}")

        except Exception as e:
            raise FileProcessingError(f"Failed to save structured DOCX: {str(e)}")

    def _apply_paragraph_formatting(self, paragraph, formatting: Dict[str, Any]) -> None:
        """Apply formatting to paragraph."""
        try:
            # Apply style if available
            if "style" in formatting and formatting["style"]:
                paragraph.style = formatting["style"]

            # Apply alignment
            if "alignment" in formatting and formatting["alignment"]:
                paragraph.alignment = formatting["alignment"]

        except Exception as e:
            self.logger.warning(f"Failed to apply paragraph formatting: {e}")

    def _add_translated_table(self, doc, translated_text: str, formatting: Dict[str, Any]) -> None:
        """Add translated table to document."""
        try:
            rows = translated_text.split('\n')
            if not rows:
                return

            # Parse table dimensions
            table_rows = formatting.get("rows", len(rows))
            table_cols = formatting.get("columns", len(rows[0].split('|')) if rows else 1)

            table = doc.add_table(rows=table_rows, cols=table_cols)

            for row_idx, row_text in enumerate(rows[:table_rows]):
                cells = row_text.split('|')
                for col_idx, cell_text in enumerate(cells[:table_cols]):
                    if row_idx < len(table.rows) and col_idx < len(table.rows[row_idx].cells):
                        table.rows[row_idx].cells[col_idx].text = cell_text.strip()

        except Exception as e:
            self.logger.warning(f"Failed to add translated table: {e}")
            # Fallback: add as paragraph
            doc.add_paragraph(translated_text)

    def get_supported_extensions(self) -> List[str]:
        """Get supported file extensions."""
        return [".docx"]
