# 🌍 Translator - AI-Powered File Translator

An intelligent file translation tool that preserves formatting while translating DOCX and PDF documents using AI.

## ✨ Features

- **📄 Multiple Format Support**: DOCX and PDF files
- **🤖 AI-Powered Translation**: OpenAI-Compatible APIs
- **🎨 Format Preservation**: Maintains original document structure and styling
- **⚡ Flexible Strategies**: Paragraph-level or sentence-level translation
- **📁 Batch Processing**: Translate multiple files at once
- **🔧 Easy Configuration**: YAML-based configuration management
- **🎯 Rich CLI Interface**: Beautiful command-line interface with progress indicators

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd translator

# Install dependencies
pip install -e .
```

### Configuration

1. **Initialize configuration:**
```bash
python -m translator.cli.main config --init
```

2. **Enter your API details when prompted:**
   - API Key: Your OpenAI-compatible API key
   - Base URL: API endpoint (default: https://api.openai.com/v1)
   - Model: AI model to use (default: gpt-3.5-turbo)

### Basic Usage

**Translate a single file:**
```bash
python -m translator.cli.main translate document.docx
```

**Translate with custom settings:**
```bash
python -m translator.cli.main translate document.pdf \
  --target zh-CN \
  --strategy sentence \
  --structured
```

**Batch translate files:**
```bash
python -m translator.cli.main batch ./documents/
```

## 📖 Commands

### `translate` - Translate a single file
```bash
python -m translator.cli.main translate [OPTIONS] INPUT_FILE
```

**Options:**
- `--output, -o`: Output file path (auto-generated if not specified)
- `--source, -s`: Source language (overrides config)
- `--target, -t`: Target language (overrides config)
- `--strategy`: Translation strategy (`paragraph` or `sentence`)
- `--structured`: Use structured translation for better formatting
- `--preserve-formatting/--no-preserve-formatting`: Control formatting preservation
- `--config, -c`: Custom configuration file
- `--verbose, -v`: Enable verbose logging

### `config` - Manage configuration
```bash
python -m translator.cli.main config [OPTIONS]
```

**Options:**
- `--init`: Initialize new configuration file
- `--show`: Display current configuration
- `--validate`: Validate configuration file
- `--file, -f`: Specify configuration file path

### `batch` - Batch translate files
```bash
python -m translator.cli.main batch [OPTIONS] INPUT_DIR
```

**Options:**
- `--output-dir, -o`: Output directory
- `--pattern, -p`: File pattern to match (default: `*.{docx,pdf}`)
- `--max-concurrent`: Maximum concurrent translations (default: 3)
- `--config, -c`: Configuration file path
- `--verbose, -v`: Enable verbose logging

### `info` - System information
```bash
python -m translator.cli.main info
```

### `version` - Version information
```bash
python -m translator.cli.main version
```

## ⚙️ Configuration

The configuration file (`config.yaml`) contains all settings:

```yaml
api:
  base_url: "https://api.openai.com/v1"
  api_key: "your-api-key-here"
  model: "gpt-3.5-turbo"
  timeout: 30
  max_retries: 3

translation:
  strategy: "paragraph"              # "paragraph" or "sentence"
  source_language: "auto"            # Auto-detect or specific language
  target_language: "zh-CN"           # Target language code
  preserve_formatting: true          # Preserve original formatting
  batch_size: 10                     # Segments per batch
```

### Supported Language Codes

Common language codes:
- `en`: English
- `zh-CN`: Simplified Chinese
- `zh-TW`: Traditional Chinese
- `ja`: Japanese
- `ko`: Korean
- `fr`: French
- `de`: German
- `es`: Spanish
- `ru`: Russian

## 🏗️ Architecture

```
translator/
├── src/translator/
│   ├── config/              # Configuration management
│   ├── translator_engine/   # AI translation engine
│   ├── file_processor/      # File format handlers
│   ├── services/           # High-level services
│   ├── cli/                # Command-line interface
│   └── utils/              # Utilities and helpers
├── config.example.yaml     # Configuration template
└── README.md              # This file
```

## 🔧 Development

### Running Tests

```bash
# Test translation engine
python test_translation_engine.py

# Test file processors
python test_file_processors.py

# Test CLI
python test_cli.py
```

### Project Structure

- **Translation Engine**: Handles AI-powered text translation
- **File Processors**: Extract and reconstruct document formats
- **Services**: Coordinate translation workflows
- **CLI**: User-friendly command-line interface

## 📝 Examples

### Example 1: Basic Translation
```bash
# Translate a DOCX file from English to Chinese
python -m translator.cli.main translate report.docx --target zh-CN
```

### Example 2: Structured Translation
```bash
# Use structured translation for better formatting preservation
python -m translator.cli.main translate presentation.pdf \
  --structured \
  --target ja
```

### Example 3: Batch Processing
```bash
# Translate all documents in a directory
python -m translator.cli.main batch ./input_docs/ \
  --output-dir ./translated_docs/ \
  --target fr
```

### Example 4: Custom Configuration
```bash
# Use custom configuration file
python -m translator.cli.main translate document.docx \
  --config ./custom_config.yaml \
  --verbose
```

## 🛠️ Troubleshooting

### Common Issues

1. **Configuration not found**
   ```bash
   # Initialize configuration
   python -m translator.cli.main config --init
   ```

2. **API connection errors**
   ```bash
   # Validate configuration
   python -m translator.cli.main config --validate
   ```

3. **Unsupported file format**
   ```bash
   # Check supported formats
   python -m translator.cli.main info
   ```

### Debug Mode

Enable verbose logging for detailed information:
```bash
python -m translator.cli.main translate document.docx --verbose
```

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For issues and questions, please open an issue on the project repository.