#!/usr/bin/env python3
"""
Test script for file processors.
This script tests the file processing functionality without requiring actual files.
"""

import sys
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from translator.file_processor import FileProcessorFactory
from translator.file_processor.base import TextSegment, DocumentStructure
from translator.utils import setup_logger


def test_file_processor_factory():
    """Test file processor factory functionality."""
    
    print("🏭 Testing File Processor Factory")
    print("=" * 50)
    
    # Test supported extensions
    extensions = FileProcessorFactory.get_supported_extensions()
    print(f"Supported extensions: {extensions}")
    
    # Test file type checking
    docx_path = Path("test.docx")
    pdf_path = Path("test.pdf")
    txt_path = Path("test.txt")
    
    print(f"DOCX supported: {FileProcessorFactory.is_supported(docx_path)}")
    print(f"PDF supported: {FileProcessorFactory.is_supported(pdf_path)}")
    print(f"TXT supported: {FileProcessorFactory.is_supported(txt_path)}")
    
    # Test processor creation
    config = {"backup_enabled": True}
    
    try:
        docx_processor = FileProcessorFactory.create_processor(docx_path, config)
        print(f"DOCX processor created: {docx_processor.__class__.__name__}")
        
        pdf_processor = FileProcessorFactory.create_processor(pdf_path, config)
        print(f"PDF processor created: {pdf_processor.__class__.__name__}")
        
        print("✅ Factory tests passed")
        
    except Exception as e:
        print(f"❌ Factory test failed: {e}")


def test_document_structure():
    """Test document structure classes."""
    
    print("\n📄 Testing Document Structure")
    print("=" * 50)
    
    # Create test segments
    segments = [
        TextSegment(
            text="This is the first paragraph.",
            formatting={"style": "Normal", "bold": False},
            position=0,
            segment_type="paragraph"
        ),
        TextSegment(
            text="This is a heading.",
            formatting={"style": "Heading1", "bold": True},
            position=1,
            segment_type="heading"
        ),
        TextSegment(
            text="This is another paragraph.",
            formatting={"style": "Normal", "bold": False},
            position=2,
            segment_type="paragraph"
        )
    ]
    
    # Create document structure
    doc_structure = DocumentStructure(
        segments=segments,
        metadata={"total_paragraphs": 3, "author": "Test Author"},
        original_format="docx"
    )
    
    print(f"Document format: {doc_structure.original_format}")
    print(f"Total segments: {len(doc_structure.segments)}")
    print(f"Metadata: {doc_structure.metadata}")
    
    # Test segment access
    for i, segment in enumerate(doc_structure.segments):
        print(f"  Segment {i}: [{segment.segment_type}] {segment.text[:30]}...")
    
    print("✅ Document structure tests passed")


def test_processor_interfaces():
    """Test processor interface methods."""
    
    print("\n🔧 Testing Processor Interfaces")
    print("=" * 50)
    
    config = {
        "backup_enabled": True,
        "preserve_styles": True,
        "page_size": "A4",
        "font_size": 12
    }
    
    # Test DOCX processor
    docx_path = Path("test.docx")
    docx_processor = FileProcessorFactory.create_processor(docx_path, config)
    
    print(f"DOCX processor config: {docx_processor.config}")
    print(f"DOCX supported extensions: {docx_processor.get_supported_extensions()}")
    print(f"DOCX backup enabled: {docx_processor.backup_enabled}")
    
    # Test PDF processor
    pdf_path = Path("test.pdf")
    pdf_processor = FileProcessorFactory.create_processor(pdf_path, config)
    
    print(f"PDF processor config: {pdf_processor.config}")
    print(f"PDF supported extensions: {pdf_processor.get_supported_extensions()}")
    print(f"PDF backup enabled: {pdf_processor.backup_enabled}")
    
    # Test file validation (will fail since files don't exist, but tests the method)
    print(f"DOCX file validation (non-existent): {docx_processor.validate_file(docx_path)}")
    print(f"PDF file validation (non-existent): {pdf_processor.validate_file(pdf_path)}")
    
    print("✅ Processor interface tests passed")


def test_processor_registration():
    """Test dynamic processor registration."""
    
    print("\n📝 Testing Processor Registration")
    print("=" * 50)
    
    # Check initial extensions
    initial_extensions = FileProcessorFactory.get_supported_extensions()
    print(f"Initial extensions: {initial_extensions}")
    
    # Create a dummy processor class
    class DummyProcessor:
        def __init__(self, config):
            self.config = config
    
    # Register new processor
    FileProcessorFactory.register_processor('.txt', DummyProcessor)
    
    # Check updated extensions
    updated_extensions = FileProcessorFactory.get_supported_extensions()
    print(f"Updated extensions: {updated_extensions}")
    
    # Test creation of new processor
    txt_path = Path("test.txt")
    print(f"TXT now supported: {FileProcessorFactory.is_supported(txt_path)}")
    
    try:
        txt_processor = FileProcessorFactory.create_processor(txt_path, {})
        print(f"TXT processor created: {txt_processor.__class__.__name__}")
        print("✅ Processor registration tests passed")
    except Exception as e:
        print(f"❌ Processor registration test failed: {e}")


def main():
    """Run all tests."""
    
    # Setup logging
    setup_logger(level="INFO")
    
    print("🧪 File Processor Test Suite")
    print("=" * 60)
    
    try:
        # Run tests
        test_file_processor_factory()
        test_document_structure()
        test_processor_interfaces()
        test_processor_registration()
        
        print("\n🎉 All file processor tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
