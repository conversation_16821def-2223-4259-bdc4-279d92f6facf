#!/usr/bin/env python3
"""
Test script for translation engine functionality.
This script tests the core translation engine without requiring actual API keys.
"""

import asyncio
import sys
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from translator.config import TranslatorConfig, APIConfig, TranslationConfig
from translator.translator_engine import TranslationEngine
from translator.translator_engine.strategies import get_strategy
from translator.utils import setup_logger


async def test_translation_strategies():
    """Test translation strategies without API calls."""
    
    print("🧪 Testing Translation Strategies")
    print("=" * 50)
    
    # Test text
    test_text = """This is the first paragraph. It contains multiple sentences.

This is the second paragraph. It also has several sentences for testing purposes.

This is the third paragraph."""
    
    # Test paragraph strategy
    print("\n📝 Testing Paragraph Strategy:")
    paragraph_strategy = get_strategy("paragraph", {})
    paragraph_segments = paragraph_strategy.segment_text(test_text)
    
    print(f"Segments found: {len(paragraph_segments)}")
    for i, segment in enumerate(paragraph_segments):
        print(f"  {i+1}. [{segment.segment_type}] {segment.text[:50]}...")
    
    # Test reconstruction
    mock_translations = [f"翻译段落{i+1}" for i in range(len(paragraph_segments))]
    reconstructed = paragraph_strategy.reconstruct_text(paragraph_segments, mock_translations)
    print(f"Reconstructed: {reconstructed}")
    
    # Test sentence strategy
    print("\n📝 Testing Sentence Strategy:")
    sentence_strategy = get_strategy("sentence", {})
    sentence_segments = sentence_strategy.segment_text(test_text)
    
    print(f"Segments found: {len(sentence_segments)}")
    for i, segment in enumerate(sentence_segments[:5]):  # Show first 5
        print(f"  {i+1}. [{segment.segment_type}] {segment.text}")
    
    print("✅ Strategy tests completed")


def test_configuration():
    """Test configuration management."""
    
    print("\n🔧 Testing Configuration Management")
    print("=" * 50)
    
    # Create test configuration
    config = TranslatorConfig.create_default(
        api_key="test-key",
        base_url="https://api.example.com/v1"
    )
    
    print(f"API Base URL: {config.api.base_url}")
    print(f"Model: {config.api.model}")
    print(f"Strategy: {config.translation.strategy}")
    print(f"Source Language: {config.translation.source_language}")
    print(f"Target Language: {config.translation.target_language}")
    
    # Test saving and loading
    test_config_path = Path("test_config.yaml")
    try:
        config.to_file(test_config_path)
        loaded_config = TranslatorConfig.from_file(test_config_path)
        
        assert loaded_config.api.api_key == config.api.api_key
        assert loaded_config.api.base_url == config.api.base_url
        print("✅ Configuration save/load test passed")
        
    finally:
        # Cleanup
        if test_config_path.exists():
            test_config_path.unlink()


async def test_translation_engine_init():
    """Test translation engine initialization."""
    
    print("\n🚀 Testing Translation Engine Initialization")
    print("=" * 50)
    
    # Create test configuration
    config = TranslatorConfig.create_default(
        api_key="test-key",
        base_url="https://api.example.com/v1"
    )
    
    try:
        # Initialize engine
        engine = TranslationEngine(config)
        
        print(f"Engine initialized successfully")
        print(f"Strategy: {engine.strategy.__class__.__name__}")
        print(f"Translator: {engine.translator.__class__.__name__}")
        
        # Test configuration summary
        summary = engine.get_config_summary()
        print(f"Config Summary: {summary}")
        
        # Test supported languages
        languages = engine.get_supported_languages()
        print(f"Supported languages: {len(languages)} languages")
        print(f"Sample languages: {languages[:10]}")
        
        print("✅ Engine initialization test passed")
        
    except Exception as e:
        print(f"❌ Engine initialization failed: {e}")


async def main():
    """Run all tests."""
    
    # Setup logging
    setup_logger(level="INFO")
    
    print("🧪 Translation Engine Test Suite")
    print("=" * 60)
    
    try:
        # Run tests
        test_configuration()
        await test_translation_strategies()
        await test_translation_engine_init()
        
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
