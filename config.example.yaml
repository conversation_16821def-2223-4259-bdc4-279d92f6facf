# Translator Configuration Example
# Copy this file to config.yaml and modify as needed

api:
  # OpenAI-Compatible API configuration
  base_url: "https://localhost:8000/v1"  # Change to your API endpoint
  api_key: "your-api-key-here"           # Your API key
  model: "ByteDance-Seed/Seed-X-PPO-7B"                 # Model to use for translation
  timeout: 60                            # Request timeout in seconds
  max_retries: 3                         # Maximum retries for failed requests

translation:
  # Translation behavior settings
  strategy: "paragraph"                  # "paragraph" or "sentence"
  source_language: "auto"                # Source language (auto-detect)
  target_language: "zh-CN"               # Target language code
  preserve_formatting: true              # Preserve original formatting
  batch_size: 10                         # Segments per batch

# Common language codes:
# - en: English
# - zh-CN: Simplified Chinese
# - zh-TW: Traditional Chinese
# - ja: Japanese
# - ko: Korean
# - fr: French
# - de: German
# - es: Spanish
# - ru: Russian
