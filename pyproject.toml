[project]
name = "translator"
version = "0.1.0"
description = "AI-powered file translator for DOCX and PDF files"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "python-docx>=1.1.0",
    "pdfplumber>=0.10.0",
    "reportlab>=4.0.0",
    "openai>=1.0.0",
    "typer>=0.9.0",
    "pydantic>=2.0.0",
    "rich>=13.0.0",
    "pyyaml>=6.0.0",
]

[project.scripts]
translator = "translator.cli:app"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/translator"]
