[project]
name = "translator"
version = "0.1.0"
description = "AI-powered file translator for DOCX and PDF files"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "openai>=1.99.9",
    "pdfplumber>=0.11.7",
    "pydantic>=2.11.7",
    "python-docx>=1.2.0",
    "pyyaml>=6.0.2",
    "reportlab>=4.4.3",
    "rich>=14.1.0",
    "typer>=0.16.0",
]

[project.scripts]
translator = "translator.cli:app"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/translator"]
