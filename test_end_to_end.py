#!/usr/bin/env python3
"""
End-to-end test script for the translator application.
This script tests the complete workflow without requiring actual API calls.
"""

import sys
import tempfile
import asyncio
from pathlib import Path
from docx import Document
from reportlab.platypus import SimpleDocTemplate, Paragraph
from reportlab.lib.styles import getSampleStyleSheet

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from translator.config import TranslatorConfig
from translator.services import FileTranslationService
from translator.file_processor import FileProcessorFactory
from translator.translator_engine import TranslationEngine
from translator.utils import setup_logger


def create_test_docx(file_path: Path) -> None:
    """Create a test DOCX file."""
    doc = Document()
    
    # Add title
    title = doc.add_heading('Test Document', 0)
    
    # Add paragraphs
    doc.add_paragraph('This is the first paragraph of the test document.')
    doc.add_paragraph('This is the second paragraph with some more content.')
    
    # Add a table
    table = doc.add_table(rows=2, cols=2)
    table.cell(0, 0).text = 'Header 1'
    table.cell(0, 1).text = 'Header 2'
    table.cell(1, 0).text = 'Data 1'
    table.cell(1, 1).text = 'Data 2'
    
    doc.add_paragraph('This is the final paragraph.')
    
    doc.save(file_path)
    print(f"✅ Created test DOCX: {file_path}")


def create_test_pdf(file_path: Path) -> None:
    """Create a test PDF file."""
    doc = SimpleDocTemplate(str(file_path))
    styles = getSampleStyleSheet()
    
    story = []
    story.append(Paragraph("Test PDF Document", styles['Title']))
    story.append(Paragraph("This is the first paragraph of the test PDF.", styles['Normal']))
    story.append(Paragraph("This is the second paragraph with more content.", styles['Normal']))
    story.append(Paragraph("This is the final paragraph.", styles['Normal']))
    
    doc.build(story)
    print(f"✅ Created test PDF: {file_path}")


async def test_file_processing():
    """Test file processing functionality."""
    print("📄 Testing File Processing")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create test files
        docx_file = temp_path / "test.docx"
        pdf_file = temp_path / "test.pdf"
        
        create_test_docx(docx_file)
        create_test_pdf(pdf_file)
        
        # Test DOCX processing
        print("Testing DOCX processing...")
        docx_processor = FileProcessorFactory.create_processor(docx_file, {})
        
        # Extract text
        docx_text = docx_processor.extract_text(docx_file)
        print(f"DOCX text length: {len(docx_text)} characters")
        assert len(docx_text) > 0
        
        # Extract structure
        docx_structure = docx_processor.extract_structure(docx_file)
        print(f"DOCX segments: {len(docx_structure.segments)}")
        assert len(docx_structure.segments) > 0
        
        # Test PDF processing
        print("Testing PDF processing...")
        pdf_processor = FileProcessorFactory.create_processor(pdf_file, {})
        
        # Extract text
        pdf_text = pdf_processor.extract_text(pdf_file)
        print(f"PDF text length: {len(pdf_text)} characters")
        assert len(pdf_text) > 0
        
        # Extract structure
        pdf_structure = pdf_processor.extract_structure(pdf_file)
        print(f"PDF segments: {len(pdf_structure.segments)}")
        assert len(pdf_structure.segments) > 0
        
        print("✅ File processing tests passed")


async def test_mock_translation():
    """Test translation with mock translator."""
    print("\n🤖 Testing Mock Translation")
    print("=" * 50)
    
    # Create mock configuration
    config = TranslatorConfig.create_default(
        api_key="test-key",
        base_url="https://api.example.com/v1"
    )
    
    # Create mock translation engine (this will fail API calls, but tests structure)
    try:
        engine = TranslationEngine(config)
        print(f"Translation engine created: {engine.__class__.__name__}")
        
        # Test configuration
        summary = engine.get_config_summary()
        print(f"Engine config: {summary}")
        
        # Test supported languages
        languages = engine.get_supported_languages()
        print(f"Supported languages: {len(languages)} languages")
        
        print("✅ Mock translation engine tests passed")
        
    except Exception as e:
        print(f"⚠️ Translation engine test failed (expected): {e}")


async def test_service_integration():
    """Test service integration."""
    print("\n🔧 Testing Service Integration")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create test file
        docx_file = temp_path / "test.docx"
        create_test_docx(docx_file)
        
        # Create mock configuration
        config = TranslatorConfig.create_default(
            api_key="test-key",
            base_url="https://api.example.com/v1"
        )
        
        # Create service
        service = FileTranslationService(config)
        print(f"Service created: {service.__class__.__name__}")
        
        # Test supported file types
        supported_types = service.get_supported_file_types()
        print(f"Supported file types: {supported_types}")
        assert '.docx' in supported_types
        assert '.pdf' in supported_types
        
        # Test health check (will fail due to mock API, but tests structure)
        try:
            health = await service.health_check()
            print(f"Health check: {health}")
        except Exception as e:
            print(f"⚠️ Health check failed (expected): {e}")
        
        print("✅ Service integration tests passed")


def test_configuration_management():
    """Test configuration management."""
    print("\n⚙️ Testing Configuration Management")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        config_file = temp_path / "test_config.yaml"
        
        # Create configuration
        config = TranslatorConfig.create_default(
            api_key="test-api-key",
            base_url="https://api.test.com/v1"
        )
        config.api.model = "test-model"
        config.translation.target_language = "ja"
        
        # Save configuration
        config.to_file(config_file)
        print(f"Configuration saved to: {config_file}")
        
        # Load configuration
        loaded_config = TranslatorConfig.from_file(config_file)
        
        # Verify configuration
        assert loaded_config.api.api_key == "test-api-key"
        assert loaded_config.api.base_url == "https://api.test.com/v1"
        assert loaded_config.api.model == "test-model"
        assert loaded_config.translation.target_language == "ja"
        
        print("✅ Configuration management tests passed")


def test_error_handling():
    """Test error handling."""
    print("\n🚨 Testing Error Handling")
    print("=" * 50)
    
    # Test invalid file type
    try:
        invalid_file = Path("test.txt")
        FileProcessorFactory.create_processor(invalid_file, {})
        assert False, "Should have raised an error"
    except Exception as e:
        print(f"✅ Invalid file type error handled: {e}")
    
    # Test missing configuration file
    try:
        missing_config = Path("nonexistent_config.yaml")
        TranslatorConfig.from_file(missing_config)
        assert False, "Should have raised an error"
    except Exception as e:
        print(f"✅ Missing config file error handled: {e}")
    
    print("✅ Error handling tests passed")


async def main():
    """Run all end-to-end tests."""
    
    # Setup logging
    setup_logger(level="INFO")
    
    print("🧪 End-to-End Test Suite")
    print("=" * 60)
    
    try:
        # Run tests
        await test_file_processing()
        await test_mock_translation()
        await test_service_integration()
        test_configuration_management()
        test_error_handling()
        
        print("\n🎉 All end-to-end tests completed successfully!")
        print("\n📋 Test Summary:")
        print("  ✅ File processing (DOCX & PDF)")
        print("  ✅ Translation engine structure")
        print("  ✅ Service integration")
        print("  ✅ Configuration management")
        print("  ✅ Error handling")
        
        print("\n💡 Next Steps:")
        print("  1. Set up a real API key in config.yaml")
        print("  2. Test with actual translation API")
        print("  3. Try translating real documents")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
